# 极简runforever编译
SOURCE = runforever_simple.c
CFLAGS = -Os -s -static

# 默认编译本地版本
all: runforever

# 本地版本（当前架构）
runforever: $(SOURCE)
	gcc $(CFLAGS) -o runforever $(SOURCE)
	strip runforever

# arm64版本（需要交叉编译工具链）
arm64: $(SOURCE)
	aarch64-linux-gnu-gcc $(CFLAGS) -o runforever_arm64 $(SOURCE)
	strip runforever_arm64

# 清理
clean:
	rm -f $(TARGET) $(TARGET)_* *.o

# 安装
install: $(TARGET)
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)

# 卸载
uninstall:
	sudo rm -f /usr/local/bin/$(TARGET)

.PHONY: all clean install uninstall x86_64 arm64 static arm64-static
