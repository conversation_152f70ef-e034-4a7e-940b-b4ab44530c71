# 极简runforever - 只编译arm64版本
CC = aarch64-linux-gnu-gcc
CFLAGS = -Os -s -static
TARGET = runforever
SOURCE = runforever_simple.c

# 默认编译arm64静态版本
all: $(TARGET)

$(TARGET): $(SOURCE)
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE)
	strip $(TARGET)

# 清理
clean:
	rm -f $(TARGET) $(TARGET)_* *.o

# 安装
install: $(TARGET)
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)

# 卸载
uninstall:
	sudo rm -f /usr/local/bin/$(TARGET)

.PHONY: all clean install uninstall x86_64 arm64 static arm64-static
