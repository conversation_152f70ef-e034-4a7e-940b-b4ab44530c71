CC = gcc
CFLAGS = -Wall -Wextra -O2 -std=c99
TARGET = runforever
SOURCE = runforever.c

# 默认目标
all: $(TARGET)

# 编译runforever
$(TARGET): $(SOURCE)
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE)

# x86_64版本
x86_64:
	$(CC) $(CFLAGS) -o $(TARGET)_x86_64 $(SOURCE)

# arm64版本
arm64:
	aarch64-linux-gnu-gcc $(CFLAGS) -o $(TARGET)_arm64 $(SOURCE)

# 静态链接版本（便于部署）
static:
	$(CC) $(CFLAGS) -static -o $(TARGET)_static $(SOURCE)

# arm64静态链接版本
arm64-static:
	aarch64-linux-gnu-gcc $(CFLAGS) -static -o $(TARGET)_arm64_static $(SOURCE)

# 清理
clean:
	rm -f $(TARGET) $(TARGET)_* *.o

# 安装
install: $(TARGET)
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)

# 卸载
uninstall:
	sudo rm -f /usr/local/bin/$(TARGET)

.PHONY: all clean install uninstall x86_64 arm64 static arm64-static
