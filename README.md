# RunForever - 进程监控重启工具

一个轻量级的进程监控工具，当被监控的程序崩溃或退出时自动重启。

## 功能特性

- 自动监控和重启崩溃的程序
- 支持命令行参数传递
- 详细的日志记录（包含时间戳）
- 优雅的信号处理（SIGINT/SIGTERM）
- 跨平台支持（x86_64、arm64）
- 防止快速重启循环（1秒延迟）

## 编译方法

### 本地编译
```bash
make
```

### 交叉编译arm64版本
```bash
# 需要先安装交叉编译工具链
sudo apt-get install gcc-aarch64-linux-gnu
make arm64
```

### 静态链接版本（便于部署）
```bash
make static          # x86_64静态版本
make arm64-static    # arm64静态版本
```

## 使用方法

### 基本用法
```bash
./runforever /path/to/your/program
```

### 带参数的程序
```bash
./runforever /usr/bin/python3 /path/to/script.py --arg1 value1
```

### 后台运行
```bash
nohup ./runforever /path/to/your/program > runforever.log 2>&1 &
```

### 使用systemd管理
创建服务文件 `/etc/systemd/system/myapp.service`：
```ini
[Unit]
Description=My Application with RunForever
After=network.target

[Service]
Type=simple
User=myuser
WorkingDirectory=/path/to/app
ExecStart=/usr/local/bin/runforever /path/to/your/program
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable myapp.service
sudo systemctl start myapp.service
```

## 安装

```bash
make install    # 安装到 /usr/local/bin/
```

## 卸载

```bash
make uninstall
```

## 日志格式

程序会输出带时间戳的日志：
```
[Mon Jan 15 10:30:45 2024] 开始监控程序: /usr/bin/myapp
[Mon Jan 15 10:31:20 2024] 程序正常退出，退出码: 1，重启次数: 1
[Mon Jan 15 10:31:21 2024] 1秒后重启程序...
[Mon Jan 15 10:31:22 2024] 程序被信号终止，信号: 9，重启次数: 2
```

## 信号处理

- `SIGINT` (Ctrl+C): 优雅退出，先终止被监控程序
- `SIGTERM`: 优雅退出，先终止被监控程序

## 注意事项

1. 确保被监控的程序路径正确且有执行权限
2. 如果程序频繁崩溃，检查程序本身的问题
3. 建议配合日志轮转工具使用，避免日志文件过大
4. 在生产环境中建议使用systemd等系统级服务管理器

## 与原版runforever的兼容性

这个重新实现的版本提供了与原版类似的核心功能：
- 进程监控和自动重启
- 命令行参数支持
- 信号处理

如果需要更多特定功能，请根据实际需求修改源码。
