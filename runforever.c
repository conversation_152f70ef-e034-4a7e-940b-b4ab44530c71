#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>
#include <string.h>
#include <time.h>
#include <errno.h>

static volatile int keep_running = 1;
static pid_t child_pid = 0;

void signal_handler(int sig) {
    if (sig == SIGINT || sig == SIGTERM) {
        keep_running = 0;
        if (child_pid > 0) {
            kill(child_pid, SIGTERM);
        }
    }
}

void log_message(const char* message) {
    time_t now;
    char* time_str;
    
    time(&now);
    time_str = ctime(&now);
    time_str[strlen(time_str) - 1] = '\0'; // 移除换行符
    
    printf("[%s] %s\n", time_str, message);
    fflush(stdout);
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        fprintf(stderr, "用法: %s <程序路径> [参数...]\n", argv[0]);
        fprintf(stderr, "示例: %s /usr/bin/myapp arg1 arg2\n", argv[0]);
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGCHLD, SIG_DFL);
    
    char log_buf[256];
    snprintf(log_buf, sizeof(log_buf), "开始监控程序: %s", argv[1]);
    log_message(log_buf);
    
    int restart_count = 0;
    
    while (keep_running) {
        child_pid = fork();
        
        if (child_pid == 0) {
            // 子进程：执行目标程序
            execvp(argv[1], &argv[1]);
            // 如果execvp返回，说明执行失败
            perror("execvp失败");
            exit(1);
        } else if (child_pid > 0) {
            // 父进程：等待子进程结束
            int status;
            pid_t result = waitpid(child_pid, &status, 0);
            
            if (result == -1) {
                if (errno == EINTR) {
                    continue; // 被信号中断，继续等待
                }
                perror("waitpid失败");
                break;
            }
            
            if (!keep_running) {
                log_message("收到退出信号，停止监控");
                break;
            }
            
            restart_count++;
            
            if (WIFEXITED(status)) {
                snprintf(log_buf, sizeof(log_buf), 
                    "程序正常退出，退出码: %d，重启次数: %d", 
                    WEXITSTATUS(status), restart_count);
            } else if (WIFSIGNALED(status)) {
                snprintf(log_buf, sizeof(log_buf), 
                    "程序被信号终止，信号: %d，重启次数: %d", 
                    WTERMSIG(status), restart_count);
            } else {
                snprintf(log_buf, sizeof(log_buf), 
                    "程序异常退出，重启次数: %d", restart_count);
            }
            
            log_message(log_buf);
            
            // 立即重启（根据用户需求）
            if (keep_running) {
                log_message("立即重启程序...");
                // 可以根据需要调整延迟时间，0表示立即重启
                // sleep(1); // 如果需要延迟可以取消注释
            }
        } else {
            // fork失败
            perror("fork失败");
            break;
        }
    }
    
    log_message("runforever退出");
    return 0;
}
