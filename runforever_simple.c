#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>

static volatile int keep_running = 1;
static pid_t child_pid = 0;

void signal_handler(int sig) {
    keep_running = 0;
    if (child_pid > 0) kill(child_pid, SIGTERM);
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        fprintf(stderr, "Usage: %s <program> [args...]\n", argv[0]);
        return 1;
    }
    
    signal(SIGIN<PERSON>, signal_handler);
    signal(SIGTER<PERSON>, signal_handler);
    
    while (keep_running) {
        child_pid = fork();
        
        if (child_pid == 0) {
            execvp(argv[1], &argv[1]);
            exit(1);
        } else if (child_pid > 0) {
            int status;
            waitpid(child_pid, &status, 0);
            if (!keep_running) break;
        } else {
            break;
        }
    }
    
    return 0;
}
