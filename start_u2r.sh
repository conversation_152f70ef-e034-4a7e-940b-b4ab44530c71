#!/bin/bash

# u2r启动脚本 - 使用runforever监控

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查runforever是否存在
if [ ! -f "./runforever" ]; then
    echo "runforever不存在，正在编译..."
    make
    if [ $? -ne 0 ]; then
        echo "编译runforever失败！"
        exit 1
    fi
fi

# 检查u2r程序是否存在
if [ ! -f "./u2r" ]; then
    echo "错误: u2r程序不存在！"
    echo "请确保u2r程序在当前目录下"
    exit 1
fi

# 确保u2r有执行权限
chmod +x ./u2r

echo "开始使用runforever监控u2r程序..."
echo "日志将保存到 runforever.log"
echo "查看日志: tail -f runforever.log"
echo "停止监控: pkill -f 'runforever.*u2r'"
echo ""

# 后台启动runforever监控u2r
nohup ./runforever ./u2r > runforever.log 2>&1 &

# 获取进程ID
RUNFOREVER_PID=$!
echo "runforever已启动，PID: $RUNFOREVER_PID"
echo "监控进程: ps aux | grep runforever"
echo ""

# 显示最近的日志
sleep 1
echo "=== 最近的日志 ==="
tail -10 runforever.log

echo ""
echo "=== 实时监控日志 ==="
echo "按 Ctrl+C 退出日志查看（不会停止runforever）"
tail -f runforever.log
