#!/bin/bash

# u2r启动脚本 - 使用runforever监控

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查runforever是否存在
if [ ! -f "./runforever" ]; then
    echo "runforever不存在，正在编译arm64版本..."
    make
    if [ $? -ne 0 ]; then
        echo "编译失败！请确保安装了 gcc-aarch64-linux-gnu"
        echo "Ubuntu/Debian: sudo apt-get install gcc-aarch64-linux-gnu"
        exit 1
    fi
    echo "编译完成，文件大小: $(ls -lh runforever | awk '{print $5}')"
fi

# 检查u2r程序是否存在
if [ ! -f "./u2r" ]; then
    echo "错误: u2r程序不存在！"
    echo "请确保u2r程序在当前目录下"
    exit 1
fi

# 确保u2r有执行权限
chmod +x ./u2r

echo "开始使用runforever监控u2r程序..."
echo "极简版本，无日志输出，资源占用最小"
echo "停止监控: pkill runforever"
echo ""

# 后台启动runforever监控u2r（无日志，最小资源占用）
nohup ./runforever ./u2r >/dev/null 2>&1 &

# 获取进程ID
RUNFOREVER_PID=$!
echo "runforever已启动，PID: $RUNFOREVER_PID"
echo "监控进程: ps aux | grep runforever"
echo "u2r进程: ps aux | grep u2r"
