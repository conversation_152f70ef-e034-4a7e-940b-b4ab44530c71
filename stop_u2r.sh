#!/bin/bash

# 停止u2r监控脚本

echo "正在查找runforever进程..."

# 查找runforever进程
PIDS=$(pgrep -f "runforever.*u2r")

if [ -z "$PIDS" ]; then
    echo "没有找到运行中的runforever监控u2r的进程"
    exit 0
fi

echo "找到以下runforever进程:"
ps aux | grep "runforever.*u2r" | grep -v grep

echo ""
echo "正在停止runforever进程..."

# 优雅地停止runforever进程
for pid in $PIDS; do
    echo "发送SIGTERM信号给进程 $pid"
    kill -TERM $pid
done

# 等待进程退出
sleep 2

# 检查是否还有进程在运行
REMAINING=$(pgrep -f "runforever.*u2r")
if [ -n "$REMAINING" ]; then
    echo "进程仍在运行，强制终止..."
    for pid in $REMAINING; do
        echo "发送SIGKILL信号给进程 $pid"
        kill -KILL $pid
    done
fi

echo "runforever监控已停止"

# 检查u2r进程是否还在运行
U2R_PIDS=$(pgrep -f "u2r")
if [ -n "$U2R_PIDS" ]; then
    echo ""
    echo "发现u2r进程仍在运行:"
    ps aux | grep "u2r" | grep -v grep
    echo ""
    read -p "是否也要停止u2r进程? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for pid in $U2R_PIDS; do
            echo "停止u2r进程 $pid"
            kill -TERM $pid
        done
        sleep 1
        # 强制终止如果还在运行
        U2R_REMAINING=$(pgrep -f "u2r")
        if [ -n "$U2R_REMAINING" ]; then
            for pid in $U2R_REMAINING; do
                kill -KILL $pid
            done
        fi
        echo "u2r进程已停止"
    fi
fi

echo "完成"
