#!/bin/bash

echo "=== RunForever 使用演示 ==="

# 1. 编译runforever
echo "1. 编译runforever..."
make clean && make

if [ ! -f "./runforever" ]; then
    echo "编译失败！"
    exit 1
fi

# 2. 创建一个测试程序u2r（模拟您的程序）
echo "2. 创建测试程序u2r..."
cat > u2r.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>

int main() {
    printf("u2r程序启动，PID: %d\n", getpid());
    
    int count = 0;
    while (1) {
        printf("u2r运行中... 计数: %d\n", ++count);
        sleep(2);
        
        // 模拟程序偶尔崩溃
        if (count == 5) {
            printf("u2r程序模拟崩溃！\n");
            exit(1);
        }
        
        if (count == 12) {
            printf("u2r程序正常退出\n");
            exit(0);
        }
    }
    
    return 0;
}
EOF

gcc -o u2r u2r.c
chmod +x u2r

echo "3. 测试runforever监控u2r..."
echo "   使用方式: nohup ./runforever ./u2r > runforever.log 2>&1 &"
echo ""
echo "现在开始演示（前台运行，方便观察）:"
echo "按 Ctrl+C 可以停止监控"
echo ""

# 前台运行演示
./runforever ./u2r
